<script setup lang="ts">
import { useCounterStore } from '../store/counter'
import { useUserStore } from '../store/user'

const counter = useCounterStore()
const user = useUserStore()
</script>

<template>
  <div class="min-h-screen bg-gray-50">
    <div class="max-w-5xl mx-auto p-6">
      <div class="text-center mb-12">
        <h1 class="text-4xl font-bold text-gray-800 mb-4">Vue3 + TypeScript + Tailwind 示例项目</h1>
        <p class="text-lg text-gray-600">使用最现代的技术栈构建美观、高效的Web应用</p>
      </div>

      <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-10">
        <div class="bg-white rounded-lg shadow-md p-6 hover:shadow-lg transition-shadow duration-300">
          <h2 class="text-xl font-semibold text-gray-800 mb-3">Vue 3 组合式API</h2>
          <p class="text-gray-600 mb-4">现代化的响应式编程方式，提供更好的代码组织和复用能力</p>
          <div class="text-blue-500 font-medium">已集成 ✓</div>
        </div>

        <div class="bg-white rounded-lg shadow-md p-6 hover:shadow-lg transition-shadow duration-300">
          <h2 class="text-xl font-semibold text-gray-800 mb-3">TypeScript 支持</h2>
          <p class="text-gray-600 mb-4">类型安全的开发体验，提高代码质量和开发效率</p>
          <div class="text-blue-500 font-medium">已集成 ✓</div>
        </div>

        <div class="bg-white rounded-lg shadow-md p-6 hover:shadow-lg transition-shadow duration-300">
          <h2 class="text-xl font-semibold text-gray-800 mb-3">Tailwind CSS</h2>
          <p class="text-gray-600 mb-4">实用优先的CSS框架，快速构建现代化界面</p>
          <div class="text-blue-500 font-medium">已集成 ✓</div>
        </div>
      </div>

      <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div class="bg-white rounded-lg shadow-md p-6">
          <h2 class="text-xl font-semibold text-gray-800 mb-4">欢迎，{{ user.name }}</h2>
          <p class="text-gray-600 mb-4">这是一个使用Vue Router实现的多页面应用示例。您可以在页面之间导航，并体验单页应用的流畅体验。</p>
          <p class="text-gray-600 mb-4">当前计数：<span class="font-semibold">{{ counter.count }}</span></p>
          <div class="flex gap-2">
            <button @click="counter.increment()" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-md">
              计数+1
            </button>
            <button @click="counter.reset()" class="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-md">
              重置计数
            </button>
          </div>
        </div>

        <div class="bg-white rounded-lg shadow-md p-6">
          <h2 class="text-xl font-semibold text-gray-800 mb-4">功能导航</h2>
          <div class="space-y-2">
            <router-link to="/about" class="block w-full text-left px-4 py-2 bg-gray-100 hover:bg-gray-200 rounded-md transition-colors">
              关于页面
            </router-link>
            <router-link to="/counter" class="block w-full text-left px-4 py-2 bg-gray-100 hover:bg-gray-200 rounded-md transition-colors">
              计数器示例
            </router-link>
            <router-link to="/user" class="block w-full text-left px-4 py-2 bg-gray-100 hover:bg-gray-200 rounded-md transition-colors">
              用户信息
            </router-link>
          </div>
        </div>
      </div>
    </div>
  </div>
</template> 