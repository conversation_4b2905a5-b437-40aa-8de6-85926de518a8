<script setup lang="ts">
import { useCounterStore } from '../store/counter'
import { ref } from 'vue'

const counter = useCounterStore()
const incrementAmount = ref(1)
</script>

<template>
    <div class="min-h-screen bg-gray-50 py-10">
        <div class="max-w-3xl mx-auto">
            <div class="bg-white rounded-lg shadow-md overflow-hidden">
                <div class="p-6 sm:p-8">
                    <h1 class="text-3xl font-bold text-gray-800 mb-6">计数器演示</h1>

                    <div class="p-6 mb-6 bg-gray-100 rounded-lg">
                        <div class="text-center">
                            <div class="text-6xl font-bold text-gray-800 mb-3">{{ counter.count }}</div>
                            <div class="text-xl text-gray-600">当前计数值</div>
                        </div>

                        <div class="mt-4 text-center">
                            <div class="text-3xl font-semibold text-blue-600 mb-2">{{ counter.doubleCount }}</div>
                            <div class="text-gray-600">双倍计数值 (Getter)</div>
                        </div>
                    </div>

                    <div class="grid grid-cols-2 gap-4 mb-8">
                        <button @click="counter.increment()"
                            class="bg-blue-500 hover:bg-blue-600 text-white py-3 px-4 rounded-md text-lg font-medium transition-colors">
                            增加 (+1)
                        </button>

                        <button @click="counter.decrement()"
                            class="bg-red-500 hover:bg-red-600 text-white py-3 px-4 rounded-md text-lg font-medium transition-colors">
                            减少 (-1)
                        </button>
                    </div>

                    <div class="mb-6">
                        <label class="block text-gray-700 font-medium mb-2">自定义增加数值:</label>
                        <div class="flex gap-3">
                            <input v-model="incrementAmount" type="number" min="1"
                                class="flex-1 border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500" />
                            <button @click="counter.incrementBy(Number(incrementAmount))"
                                class="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-md font-medium transition-colors">
                                增加
                            </button>
                        </div>
                    </div>

                    <button @click="counter.reset()"
                        class="w-full bg-gray-500 hover:bg-gray-600 text-white py-2 px-4 rounded-md font-medium transition-colors">
                        重置计数器
                    </button>
                </div>

                <div class="bg-gray-100 p-4 text-center">
                    <router-link to="/" class="text-blue-600 hover:text-blue-800 font-medium">
                        返回首页
                    </router-link>
                </div>
            </div>

            <div class="mt-8 bg-white p-6 rounded-lg shadow-md">
                <h2 class="text-xl font-semibold text-gray-800 mb-4">Pinia 状态管理说明</h2>
                <p class="text-gray-600 mb-4">
                    这个计数器示例使用 Pinia 进行状态管理。Pinia 是 Vue 的最新状态管理库，它简化了 Vuex 的概念，提供了更轻量的 API。
                </p>
                <p class="text-gray-600">
                    您在此页面的操作会影响全局状态，导航到其他页面后计数值仍然保持，这展示了 Pinia 全局状态管理的能力。
                </p>
            </div>
        </div>
    </div>
</template>