<script setup lang="ts">
</script>

<template>
    <div class="min-h-screen bg-gray-50 py-10">
        <div class="max-w-4xl mx-auto p-6 bg-white rounded-lg shadow-md">
            <h1 class="text-3xl font-bold text-gray-800 mb-6">关于我们</h1>

            <div class="prose prose-lg max-w-none">
                <p class="mb-4">
                    这是一个使用Vue 3、TypeScript和Tailwind CSS构建的示例项目，用于展示现代前端技术栈的强大功能。
                </p>

                <h2 class="text-2xl font-semibold text-gray-800 mt-8 mb-4">技术栈</h2>

                <ul class="list-disc pl-6 mb-6">
                    <li class="mb-2">
                        <strong>Vue 3</strong> - 渐进式JavaScript框架，提供更好的性能和组合式API
                    </li>
                    <li class="mb-2">
                        <strong>TypeScript</strong> - 添加静态类型检查，提高代码质量和开发体验
                    </li>
                    <li class="mb-2">
                        <strong>Tailwind CSS</strong> - 实用优先的CSS框架，快速构建现代界面
                    </li>
                    <li class="mb-2">
                        <strong>Vue Router</strong> - Vue.js官方路由管理器，支持嵌套路由和动态路由
                    </li>
                    <li class="mb-2">
                        <strong>Pinia</strong> - Vue的状态管理库，轻量且类型安全
                    </li>
                    <li class="mb-2">
                        <strong>Vite</strong> - 下一代前端构建工具，提供更快的开发体验
                    </li>
                </ul>

                <h2 class="text-2xl font-semibold text-gray-800 mt-8 mb-4">项目特点</h2>

                <p class="mb-4">
                    此项目展示了Vue 3生态系统的最佳实践，包括：
                </p>

                <ul class="list-disc pl-6 mb-6">
                    <li class="mb-2">基于组合式API的组件设计</li>
                    <li class="mb-2">使用Pinia进行全局状态管理</li>
                    <li class="mb-2">Vue Router实现的页面路由</li>
                    <li class="mb-2">使用TypeScript进行类型安全开发</li>
                    <li class="mb-2">使用Tailwind CSS设计的响应式UI</li>
                </ul>

                <p class="mb-4">
                    这个示例项目可以作为新项目的起点，或者用于学习最新的Vue 3开发技术。
                </p>
            </div>

            <div class="mt-8 pt-6 border-t border-gray-200">
                <router-link to="/"
                    class="inline-block bg-blue-500 hover:bg-blue-600 text-white font-medium px-5 py-2 rounded-md transition-colors">
                    返回首页
                </router-link>
            </div>
        </div>
    </div>
</template>