import { defineStore } from 'pinia'
import { ref, computed } from 'vue'

// 使用组合式API方式定义store
export const useUserStore = defineStore('user', () => {
  // 状态 (ref())
  const name = ref('张三')
  const age = ref(25)
  const isLoggedIn = ref(false)

  // 计算属性 (computed())
  const nameAndAge = computed(() => `${name.value} (${age.value}岁)`)
  
  // 操作 (function())
  function login() {
    isLoggedIn.value = true
  }
  
  function logout() {
    isLoggedIn.value = false
  }
  
  function updateName(newName: string) {
    name.value = newName
  }
  
  function updateAge(newAge: number) {
    age.value = newAge
  }

  // 暴露状态和方法
  return {
    // 状态
    name,
    age,
    isLoggedIn,
    // 计算属性
    nameAndAge,
    // 操作
    login,
    logout,
    updateName,
    updateAge
  }
}) 