<script setup lang="ts">
import TheNavbar from './TheNavbar.vue'
</script>

<template>
    <div class="min-h-screen flex flex-col">
        <TheNavbar />

        <main class="flex-grow">
            <router-view v-slot="{ Component }">
                <transition name="fade" mode="out-in">
                    <component :is="Component" />
                </transition>
            </router-view>
        </main>

        <footer class="bg-gray-800 text-white py-6">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div class="flex flex-col md:flex-row justify-between items-center">
                    <div class="mb-4 md:mb-0">
                        <p class="text-center md:text-left">
                            Vue3 + TypeScript + Tailwind 示例项目
                        </p>
                    </div>

                    <div class="flex space-x-6">
                        <a href="https://v3.cn.vuejs.org/" target="_blank" class="text-gray-300 hover:text-white">
                            Vue3 文档
                        </a>
                        <a href="https://www.typescriptlang.org/" target="_blank"
                            class="text-gray-300 hover:text-white">
                            TypeScript
                        </a>
                        <a href="https://tailwindcss.com/" target="_blank" class="text-gray-300 hover:text-white">
                            Tailwind CSS
                        </a>
                    </div>
                </div>
            </div>
        </footer>
    </div>
</template>

<style scoped>
.fade-enter-active,
.fade-leave-active {
    transition: opacity 0.15s ease;
}

.fade-enter-from,
.fade-leave-to {
    opacity: 0;
}
</style>